I'll design a comprehensive email management system that handles multiple email providers with optimal efficiency. Let me break down the architecture and implementation step by step.

## Architecture Overview

The system will follow a modular design with these key components:

1. **Provider Abstraction Layer** - Unified interface for different email providers
2. **Configuration Management** - Account storage and provider-specific settings
3. **Email Processing Engine** - Efficient retrieval and storage
4. **Data Storage Layer** - Organized email storage system
5. **Connection Management** - Optimized connection handling

## Step-by-Step Approach for Optimal Efficiency

1. **Connection Pooling** - Reuse connections where possible
2. **Batch Processing** - Handle multiple emails in batches
3. **Incremental Sync** - Only fetch new/changed emails
4. **Async Operations** - Concurrent processing for multiple accounts
5. **Smart Caching** - <PERSON><PERSON> frequently accessed data
6. **Error Recovery** - Robust error handling and retry logicNow let me create the project structure and additional configuration files:

"""
Additional modules for the Email Agent System
"""

# ==== src/utils/security.py ====
import os
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging

logger = logging.getLogger(__name__)

class PasswordManager:
    """Handle password encryption and decryption"""
    
    def __init__(self, key: str = None):
        if key is None:
            key = os.environ.get('EMAIL_AGENT_KEY', 'default-key-change-me')
        
        self.key = key
        self._fernet = self._create_fernet()
    
    def _create_fernet(self) -> Fernet:
        """Create Fernet encryption instance"""
        key_bytes = self.key.encode()
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'email-agent-salt',  # In production, use random salt
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(key_bytes))
        return Fernet(key)
    
    def encrypt_password(self, password: str) -> str:
        """Encrypt password"""
        try:
            encrypted = self._fernet.encrypt(password.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Error encrypting password: {e}")
            return password  # Return original if encryption fails
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """Decrypt password"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_password.encode())
            decrypted = self._fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Error decrypting password: {e}")
            return encrypted_password  # Return original if decryption fails

# Global password manager instance
password_manager = PasswordManager()

# ==== src/utils/email_parser.py ====
import re
import html
from typing import Dict, List, Tuple, Optional
from email.header import decode_header
from email.utils import parseaddr, parsedate_to_datetime
import logging

logger = logging.getLogger(__name__)

class EmailParser:
    """Utility class for parsing email content"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """Clean and normalize text content"""
        if not text:
            return ""
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # Decode HTML entities
        text = html.unescape(text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text
    
    @staticmethod
    def extract_links(text: str) -> List[str]:
        """Extract URLs from text"""
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        return re.findall(url_pattern, text)
    
    @staticmethod
    def extract_email_addresses(text: str) -> List[str]:
        """Extract email addresses from text"""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return re.findall(email_pattern, text)
    
    @staticmethod
    def parse_address(address: str) -> Tuple[str, str]:
        """Parse email address into name and email"""
        name, email = parseaddr(address)
        return name.strip(), email.strip()
    
    @staticmethod
    def decode_mime_header(header: str) -> str:
        """Decode MIME encoded header"""
        if not header:
            return ""
        
        decoded_parts = decode_header(header)
        decoded_header = ""
        
        for part, encoding in decoded_parts:
            if isinstance(part, bytes):
                try:
                    decoded_header += part.decode(encoding or 'utf-8')
                except:
                    decoded_header += part.decode('utf-8', errors='ignore')
            else:
                decoded_header += part
        
        return decoded_header.strip()
    
    @staticmethod
    def extract_metadata(email_message) -> Dict[str, str]:
        """Extract metadata from email message"""
        metadata = {}
        
        # Basic headers
        metadata['message_id'] = email_message.get('Message-ID', '')
        metadata['in_reply_to'] = email_message.get('In-Reply-To', '')
        metadata['references'] = email_message.get('References', '')
        metadata['thread_index'] = email_message.get('Thread-Index', '')
        
        # Priority and importance
        metadata['priority'] = email_message.get('X-Priority', '')
        metadata['importance'] = email_message.get('Importance', '')
        
        # Spam and security headers
        metadata['spam_score'] = email_message.get('X-Spam-Score', '')
        metadata['dkim_signature'] = email_message.get('DKIM-Signature', '')
        metadata['spf_result'] = email_message.get('Received-SPF', '')
        
        return metadata

# ==== src/providers/pop_provider.py ====
import poplib
import email
from typing import List, Optional
from ..core.models import EmailMessage, EmailAccount
from .base import EmailProviderInterface
import logging

logger = logging.getLogger(__name__)

class POPProvider(EmailProviderInterface):
    """POP3 email provider implementation"""
    
    def __init__(self, account: EmailAccount):
        self.account = account
        self.pop_connection = None
        self.smtp_connection = None
    
    async def connect(self) -> bool:
        """Establish POP3 connection"""
        try:
            if self.account.use_ssl:
                self.pop_connection = poplib.POP3_SSL(
                    self.account.pop_server,
                    self.account.pop_port
                )
            else:
                self.pop_connection = poplib.POP3(
                    self.account.pop_server,
                    self.account.pop_port
                )
                if self.account.use_tls:
                    self.pop_connection.stls()
            
            self.pop_connection.user(self.account.email)
            self.pop_connection.pass_(self.account.password)
            
            logger.info(f"POP3 connected for {self.account.email}")
            return True
            
        except Exception as e:
            logger.error(f"POP3 connection failed for {self.account.email}: {e}")
            return False
    
    async def disconnect(self):
        """Close POP3 connection"""
        try:
            if self.pop_connection:
                self.pop_connection.quit()
        except Exception as e:
            logger.error(f"Error disconnecting POP3: {e}")
    
    async def fetch_emails(self, folder: str = "INBOX", limit: int = None) -> List[EmailMessage]:
        """Fetch emails via POP3"""
        if not self.pop_connection:
            await self.connect()
        
        try:
            # Get message count
            num_messages = len(self.pop_connection.list()[1])
            
            if limit:
                start_index = max(1, num_messages - limit + 1)
            else:
                start_index = 1
            
            emails = []
            for i in range(start_index, num_messages + 1):
                try:
                    raw_email = b"\n".join(self.pop_connection.retr(i)[1])
                    email_message = email.message_from_bytes(raw_email)
                    
                    parsed_email = self._parse_email(email_message, str(i))
                    if parsed_email:
                        emails.append(parsed_email)
                        
                        # Delete if configured
                        if self.account.delete_after_read:
                            self.pop_connection.dele(i)
                            
                except Exception as e:
                    logger.error(f"Error parsing email {i}: {e}")
                    continue
            
            return emails
            
        except Exception as e:
            logger.error(f"Error fetching emails via POP3: {e}")
            return []
    
    def _parse_email(self, email_message, uid: str) -> Optional[EmailMessage]:
        """Parse email message"""
        try:
            from ..utils.email_parser import EmailParser
            
            parser = EmailParser()
            
            subject = parser.decode_mime_header(email_message.get("Subject", ""))
            sender = email_message.get("From", "")
            recipients = [email_message.get("To", "")]
            date_str = email_message.get("Date", "")
            
            # Parse date
            try:
                email_date = parsedate_to_datetime(date_str)
            except:
                email_date = datetime.now()
            
            # Extract body
            body, html_body = self._extract_body(email_message)
            
            return EmailMessage(
                uid=uid,
                subject=subject,
                sender=sender,
                recipients=recipients,
                date=email_date,
                body=body,
                html_body=html_body,
                headers=dict(email_message.items())
            )
            
        except Exception as e:
            logger.error(f"Error parsing email: {e}")
            return None
    
    def _extract_body(self, email_message) -> tuple:
        """Extract text and HTML body from email"""
        body = ""
        html_body = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if "attachment" not in content_disposition:
                    if content_type == "text/plain":
                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == "text/html":
                        html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            content_type = email_message.get_content_type()
            if content_type == "text/plain":
                body = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
            elif content_type == "text/html":
                html_body = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
        
        return body, html_body
    
    async def send_email(self, to: List[str], subject: str, body: str, 
                        html_body: str = None, attachments: List[str] = None) -> bool:
        """Send email via SMTP (same as IMAP provider)"""
        # Implementation same as IMAPProvider
        return False  # Placeholder
    
    async def delete_email(self, uid: str) -> bool:
        """Delete email (POP3 specific)"""
        try:
            if self.pop_connection:
                self.pop_connection.dele(int(uid))
                return True
        except Exception as e:
            logger.error(f"Error deleting email: {e}")
        return False
    
    async def mark_as_read(self, uid: str) -> bool:
        """Mark as read (not supported in POP3)"""
        return True  # POP3 doesn't support marking as read

# ==== tests/test_email_agent.py ====
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from src.core.email_agent import EmailAgent
from src.core.models import EmailAccount, EmailMessage
from datetime import datetime

class TestEmailAgent:
    """Test cases for EmailAgent"""
    
    @pytest.fixture
    async def email_agent(self):
        """Create email agent instance for testing"""
        agent = EmailAgent()
        await agent.initialize()
        return agent
    
    @pytest.fixture
    def sample_account(self):
        """Create sample email account"""
        return EmailAccount(
            email="<EMAIL>",
            password="test_password",
            provider="gmail",
            imap_server="imap.gmail.com",
            imap_port=993,
            smtp_server="smtp.gmail.com",
            smtp_port=587
        )
    
    @pytest.fixture
    def sample_email(self):
        """Create sample email message"""
        return EmailMessage(
            uid="test_uid_123",
            subject="Test Email",
            sender="<EMAIL>",
            recipients=["<EMAIL>"],
            date=datetime.now(),
            body="This is a test email body"
        )
    
    @pytest.mark.asyncio
    async def test_initialize_agent(self, email_agent):
        """Test agent initialization"""
        assert email_agent.account_manager is not None
        assert email_agent.email_storage is not None
        assert email_agent.providers is not None
    
    @pytest.mark.asyncio
    async def test_add_account(self, email_agent, sample_account):
        """Test adding email account"""
        with patch.object(email_agent.account_manager, 'save_account') as mock_save:
            mock_save.return_value = AsyncMock()
            
            await email_agent.add_account(
                email=sample_account.email,
                password=sample_account.password,
                provider=sample_account.provider
            )
            
            assert sample_account.email in email_agent.providers
    
    @pytest.mark.asyncio
    async def test_sync_account(self, email_agent, sample_account, sample_email):
        """Test account synchronization"""
        # Mock provider
        mock_provider = Mock()
        mock_provider.connect = AsyncMock(return_value=True)
        mock_provider.fetch_emails = AsyncMock(return_value=[sample_email])
        mock_provider.disconnect = AsyncMock()
        
        email_agent.providers[sample_account.email] = mock_provider
        email_agent.account_manager.accounts[sample_account.email] = sample_account
        
        with patch.object(email_agent.email_storage, 'email_exists') as mock_exists:
            mock_exists.return_value = False
            
            with patch.object(email_agent.email_storage, 'save_emails') as mock_save:
                mock_save.return_value = AsyncMock()
                
                await email_agent.sync_account(sample_account.email)
                
                mock_provider.connect.assert_called_once()
                mock_provider.fetch_emails.assert_called_once()
                mock_provider.disconnect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_email(self, email_agent, sample_account):
        """Test sending email"""
        mock_provider = Mock()
        mock_provider.send_email = AsyncMock(return_value=True)
        
        email_agent.providers[sample_account.email] = mock_provider
        
        result = await email_agent.send_email(
            from_address=sample_account.email,
            to=["<EMAIL>"],
            subject="Test Subject",
            body="Test Body"
        )
        
        assert result is True
        mock_provider.send_email.assert_called_once()


email-agent-system/
│
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
├── config.py              # Configuration settings
├── cli.py                 # Command-line interface
├── README.md              # Project documentation
│
├── src/
│   ├── __init__.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── email_agent.py      # Main EmailAgent class
│   │   ├── account_manager.py  # Account management
│   │   └── storage.py          # Email storage management
│   │
│   ├── providers/
│   │   ├── __init__.py
│   │   ├── base.py            # Abstract provider interface
│   │   ├── imap_provider.py   # IMAP implementation
│   │   ├── pop_provider.py    # POP3 implementation
│   │   └── config.py          # Provider configurations
│   │
│   └── utils/
│       ├── __init__.py
│       ├── email_parser.py    # Email parsing utilities
│       ├── security.py       # Security utilities
│       └── logger.py         # Logging configuration
│
├── accounts/              # Account configurations (JSON files)
│   └── .gitkeep
│
├── mail-data/            # Email storage directory
│   └── .gitkeep
│
├── logs/                 # Log files
│   └── .gitkeep
│
├── tests/
│   ├── __init__.py
│   ├── test_email_agent.py
│   ├── test_providers.py
│   └── test_storage.py
│
└── scripts/
    ├── setup.py          # Setup and installation script
    ├── backup.py         # Backup utilities
    └── migrate.py        # Migration scripts

==== requirements.txt ====
aiofiles==23.2.1
asyncio==3.4.3
cryptography==41.0.7
python-dotenv==1.0.0
click==8.1.7
colorama==0.4.6
tabulate==0.9.0
rich==13.7.0
pydantic==2.5.0
python-dateutil==2.8.2

==== config.py ====
import os
from typing import Dict, Any
from dataclasses import dataclass

@dataclass
class AppConfig:
    """Application configuration"""
    accounts_dir: str = "accounts"
    storage_dir: str = "mail-data"
    logs_dir: str = "logs"
    sync_interval: int = 300  # seconds
    max_workers: int = 4
    batch_size: int = 50
    max_retries: int = 3
    retry_delay: int = 5
    
    # Security settings
    encrypt_passwords: bool = True
    encryption_key: str = None
    
    # Performance settings
    enable_caching: bool = True
    cache_size: int = 1000
    connection_timeout: int = 30
    
    # Email processing settings
    parse_attachments: bool = True
    max_attachment_size: int = 50 * 1024 * 1024  # 50MB
    allowed_file_types: list = None
    
    def __post_init__(self):
        if self.allowed_file_types is None:
            self.allowed_file_types = ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.png']
        
        # Create directories
        for directory in [self.accounts_dir, self.storage_dir, self.logs_dir]:
            os.makedirs(directory, exist_ok=True)
        
        # Set encryption key if not provided
        if self.encrypt_passwords and not self.encryption_key:
            self.encryption_key = os.environ.get('EMAIL_AGENT_KEY', 'default-key-change-me')

# Global config instance
config = AppConfig()

==== cli.py ====
import click
import asyncio
import json
from rich.console import Console
from rich.table import Table
from rich.progress import Progress
from src.core.email_agent import EmailAgent

console = Console()

@click.group()
def cli():
    """Email Agent System - Multi-provider email management"""
    pass

@cli.command()
@click.option('--email', prompt=True, help='Email address')
@click.option('--password', prompt=True, hide_input=True, help='Email password')
@click.option('--provider', prompt=True, type=click.Choice(['gmail', 'outlook', 'yahoo', 'proton']))
@click.option('--delete-after-read', is_flag=True, help='Delete emails after reading')
def add_account(email, password, provider, delete_after_read):
    """Add a new email account"""
    async def _add_account():
        agent = EmailAgent()
        await agent.initialize()
        await agent.add_account(
            email=email,
            password=password,
            provider=provider,
            delete_after_read=delete_after_read
        )
        console.print(f"✅ Account {email} added successfully", style="green")
    
    asyncio.run(_add_account())

@cli.command()
def list_accounts():
    """List all configured accounts"""
    async def _list_accounts():
        agent = EmailAgent()
        await agent.initialize()
        accounts = agent.account_manager.list_accounts()
        
        if not accounts:
            console.print("No accounts configured", style="yellow")
            return
        
        table = Table(title="Email Accounts")
        table.add_column("Email", style="cyan")
        table.add_column("Provider", style="magenta")
        table.add_column("Status", style="green")
        
        for email in accounts:
            account = agent.account_manager.get_account(email)
            table.add_row(email, account.provider, "✅ Active")
        
        console.print(table)
    
    asyncio.run(_list_accounts())

@cli.command()
@click.option('--email', help='Sync specific email account')
@click.option('--all', 'sync_all', is_flag=True, help='Sync all accounts')
def sync(email, sync_all):
    """Sync emails from accounts"""
    async def _sync():
        agent = EmailAgent()
        await agent.initialize()
        
        if sync_all:
            with Progress() as progress:
                task = progress.add_task("Syncing accounts...", total=len(agent.account_manager.list_accounts()))
                
                for account_email in agent.account_manager.list_accounts():
                    progress.update(task, description=f"Syncing {account_email}")
                    await agent.sync_account(account_email)
                    progress.advance(task)
            
            console.print("✅ All accounts synced successfully", style="green")
        
        elif email:
            console.print(f"Syncing {email}...")
            await agent.sync_account(email)
            console.print(f"✅ {email} synced successfully", style="green")
        
        else:
            console.print("Please specify --email or --all", style="red")
    
    asyncio.run(_sync())

@cli.command()
@click.option('--from', 'from_email', prompt=True, help='From email address')
@click.option('--to', prompt=True, help='To email address (comma-separated)')
@click.option('--subject', prompt=True, help='Email subject')
@click.option('--body', prompt=True, help='Email body')
@click.option('--html', help='HTML body file path')
@click.option('--attachments', help='Attachment file paths (comma-separated)')
def send(from_email, to, subject, body, html, attachments):
    """Send an email"""
    async def _send():
        agent = EmailAgent()
        await agent.initialize()
        
        to_list = [email.strip() for email in to.split(',')]
        attachment_list = [att.strip() for att in attachments.split(',')] if attachments else None
        html_body = None
        
        if html and os.path.exists(html):
            with open(html, 'r') as f:
                html_body = f.read()
        
        success = await agent.send_email(
            from_address=from_email,
            to=to_list,
            subject=subject,
            body=body,
            html_body=html_body,
            attachments=attachment_list
        )
        
        if success:
            console.print("✅ Email sent successfully", style="green")
        else:
            console.print("❌ Failed to send email", style="red")
    
    asyncio.run(_send())

@cli.command()
@click.option('--email', prompt=True, help='Email account')
@click.option('--limit', default=10, help='Number of emails to show')
def list_emails(email, limit):
    """List emails for an account"""
    async def _list_emails():
        agent = EmailAgent()
        await agent.initialize()
        
        emails = await agent.get_emails(email)
        emails = sorted(emails, key=lambda x: x.date, reverse=True)[:limit]
        
        if not emails:
            console.print(f"No emails found for {email}", style="yellow")
            return
        
        table = Table(title=f"Latest {len(emails)} emails for {email}")
        table.add_column("Date", style="cyan")
        table.add_column("From", style="magenta")
        table.add_column("Subject", style="white")
        table.add_column("Size", style="green")
        
        for email_msg in emails:
            date_str = email_msg.date.strftime("%Y-%m-%d %H:%M")
            subject = email_msg.subject[:50] + "..." if len(email_msg.subject) > 50 else email_msg.subject
            size = f"{len(email_msg.body)} chars"
            table.add_row(date_str, email_msg.sender, subject, size)
        
        console.print(table)
    
    asyncio.run(_list_emails())

@cli.command()
@click.option('--interval', default=300, help='Sync interval in seconds')
def daemon(interval):
    """Run email agent in daemon mode"""
    async def _daemon():
        agent = EmailAgent()
        await agent.initialize()
        console.print(f"🚀 Starting email agent daemon (sync every {interval}s)", style="green")
        console.print("Press Ctrl+C to stop", style="yellow")
        await agent.run_continuous_sync(interval)
    
    asyncio.run(_daemon())

if __name__ == '__main__':
    cli()

==== README.md ====
# Email Agent System

A comprehensive multi-provider email management system that supports Gmail, Outlook, Yahoo, and ProtonMail. The system provides efficient email synchronization, storage, and management capabilities with support for both IMAP and POP3 protocols.

## Features

- **Multi-Provider Support**: Gmail, Outlook, Yahoo, ProtonMail
- **Efficient Synchronization**: Incremental sync, batch processing, async operations
- **Secure Storage**: Encrypted password storage, organized email data
- **Flexible Configuration**: JSON-based account management
- **CLI Interface**: Easy-to-use command-line tools
- **Daemon Mode**: Continuous background synchronization
- **Email Operations**: Send, receive, delete, mark as read
- **Attachment Support**: Handle email attachments
- **Error Recovery**: Robust error handling and retry logic

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd email-agent-system
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables (optional):
```bash
export EMAIL_AGENT_KEY="your-encryption-key"
```

## Quick Start

### 1. Add Email Account

```bash
python cli.py add-account
```

Follow the prompts to add your email account. For Gmail, you'll need to:
- Enable 2-factor authentication
- Generate an app-specific password
- Use the app password instead of your regular password

### 2. List Accounts

```bash
python cli.py list-accounts
```

### 3. Sync Emails

```bash
# Sync all accounts
python cli.py sync --all

# Sync specific account
python cli.py sync --email <EMAIL>
```

### 4. Send Email

```bash
python cli.py send
```

### 5. List Emails

```bash
python cli.py list-emails --email <EMAIL> --limit 20
```

### 6. Run in Daemon Mode

```bash
python cli.py daemon --interval 300
```

## Configuration

### Account Configuration

Accounts are stored in the `accounts/` directory as JSON files:

```json
{
  "email": "<EMAIL>",
  "password": "app-password",
  "provider": "gmail",
  "imap_server": "imap.gmail.com",
  "imap_port": 993,
  "smtp_server": "smtp.gmail.com",
  "smtp_port": 587,
  "use_ssl": true,
  "use_tls": true,
  "delete_after_read": false,
  "leave_on_server": true
}
```

### Provider-Specific Setup

#### Gmail
1. Enable 2-factor authentication
2. Generate app-specific password
3. Use app password in configuration

#### Outlook/Hotmail
1. Enable 2-factor authentication
2. Generate app password
3. Use app password in configuration

#### Yahoo
1. Enable 2-factor authentication
2. Generate app password
3. Use app password in configuration

#### ProtonMail
1. Install ProtonMail Bridge
2. Configure Bridge settings
3. Use Bridge credentials

## Architecture

### Core Components

1. **EmailAgent**: Main orchestrator class
2. **AccountManager**: Handles account storage and retrieval
3. **EmailStorage**: Manages email data persistence
4. **EmailProviderInterface**: Abstract interface for email providers
5. **IMAPProvider**: IMAP protocol implementation

### Design Patterns

- **Strategy Pattern**: Different email providers
- **Factory Pattern**: Provider creation
- **Observer Pattern**: Event-driven synchronization
- **Repository Pattern**: Email storage abstraction

### Performance Optimizations

1. **Connection Pooling**: Reuse email connections
2. **Batch Processing**: Handle multiple emails together
3. **Incremental Sync**: Only fetch new emails
4. **Async Operations**: Concurrent account processing
5. **Smart Caching**: Cache frequently accessed data

## API Usage

```python
import asyncio
from src.core.email_agent import EmailAgent

async def main():
    # Initialize agent
    agent = EmailAgent()
    await agent.initialize()
    
    # Add account
    await agent.add_account(
        email="<EMAIL>",
        password="app-password",
        provider="gmail"
    )
    
    # Sync emails
    await agent.sync_all_accounts()
    
    # Send email
    await agent.send_email(
        from_address="<EMAIL>",
        to=["<EMAIL>"],
        subject="Test Email",
        body="Hello from Email Agent!"
    )
    
    # Get emails
    emails = await agent.get_emails("<EMAIL>")
    print(f"Found {len(emails)} emails")

asyncio.run(main())
```

## Security Considerations

1. **Password Encryption**: Passwords are encrypted before storage
2. **Secure Connections**: SSL/TLS for all email operations
3. **App Passwords**: Use provider-specific app passwords
4. **Environment Variables**: Store sensitive data in environment
5. **File Permissions**: Restrict access to account files

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Ensure 2FA is enabled
   - Use app-specific passwords
   - Check provider settings

2. **Connection Timeouts**
   - Verify server settings
   - Check firewall/proxy settings
   - Increase timeout values

3. **ProtonMail Issues**
   - Ensure Bridge is running
   - Check Bridge port settings
   - Verify Bridge authentication

### Logging

Logs are stored in the `logs/` directory. Enable debug logging:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request

## License

MIT License - see LICENSE file for details

==== main.py ====
#!/usr/bin/env python3
"""
Email Agent System - Main Entry Point
"""

import asyncio
import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.email_agent import EmailAgent
from utils.logger import setup_logging
from config import config

async def main():
    """Main application entry point"""
    # Setup logging
    setup_logging(config.logs_dir)
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize email agent
        logger.info("Initializing Email Agent System...")
        agent = EmailAgent()
        await agent.initialize()
        
        # Example usage - customize as needed
        logger.info("Email Agent System ready!")
        
        # Uncomment to run continuous sync
        # await agent.run_continuous_sync(config.sync_interval)
        
    except KeyboardInterrupt:
        logger.info("Shutting down Email Agent System...")
    except Exception as e:
        logger.error(f"Error in main application: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

==== src/utils/logger.py ====
import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler

def setup_logging(logs_dir: str = "logs", level: int = logging.INFO):
    """Setup application logging"""
    os.makedirs(logs_dir, exist_ok=True)
    
    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_formatter = logging.Formatter(
        '%(levelname)s - %(message)s'
    )
    
    # Setup file handler
    log_file = os.path.join(logs_dir, f"email_agent_{datetime.now().strftime('%Y%m%d')}.log")
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(level)
    
    # Setup console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(logging.INFO)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Suppress noisy third-party loggers
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    logging.getLogger('aiofiles').setLevel(logging.WARNING)

==== scripts/setup.py ====
#!/usr/bin/env python3
"""
Email Agent System Setup Script
"""

import os
import sys
import subprocess
from pathlib import Path

def install_dependencies():
    """Install required Python packages"""
    print("Installing dependencies...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])

def create_directories():
    """Create necessary directories"""
    directories = ["accounts", "mail-data", "logs", "src"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"Created directory: {directory}")

def setup_environment():
    """Setup environment configuration"""
    env_file = Path(".env")
    if not env_file.exists():
        with open(env_file, 'w') as f:
            f.write("EMAIL_AGENT_KEY=change-this-encryption-key\n")
        print("Created .env file - please update the encryption key!")

def main():
    """Main setup function"""
    print("Setting up Email Agent System...")
    
    try:
        create_directories()
        install_dependencies()
        setup_environment()
        
        print("\n✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Update the encryption key in .env file")
        print("2. Add your email accounts: python cli.py add-account")
        print("3. Start syncing: python cli.py sync --all")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()