You are a genius Python engineer.

Write the full project source code and architecture for an agents able the read, send email using the main free email provides (Gmail, outlook, Proton, yahoo). THe accounts informations are stored in an account folder in a json file (with provider). The application provides the full configuration for accessing these services with mail3, POP, SMTP according to their specificities.
The application is able to retrieve all the emails of an account and stored the messages in a mail-data followed by the email adress retrieved. The application leave or delete read email according to the configuration. 

Reflect on the best step by step approach for performing these task in an optimaly efficient way.